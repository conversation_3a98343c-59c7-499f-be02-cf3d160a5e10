RSpec.shared_context "drive_api_shared_context", shared_context: :metadata do
  include_context "users_api_shared_context"

  let(:toyota) { create(:brand, name: "Toyota") }

  let(:dealership) { create(:dealership, brand: toyota) }
  let(:dealership_uuid) { dealership.uuid }
  let!(:user_dealership) { create(:user_dealership, user: user, dealership: dealership, role: :dealership_admin) }
  let(:vehicle) { create(:vehicle, dealership: dealership) }
  let(:vehicle_uuid) { vehicle.uuid }
  let(:customer) { create(:customer, dealership: dealership) }
  let(:driver_license) { create(:driver_license, holder: customer) }
  let(:sales_person) { create(:user) }
  let(:sales_person_uuid) { sales_person.uuid }
  let!(:sales_person_dealership) { create(:user_dealership, user: sales_person, dealership: dealership, role: :sales_person) }
end
