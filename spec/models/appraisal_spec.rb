require 'rails_helper'

RSpec.describe Appraisal, type: :model do
  describe 'associations' do
    it { should belong_to(:dealership) }
    it { should belong_to(:customer) }
    it { should have_one(:customer_vehicle).dependent(:destroy) }
    it { should belong_to(:sales_person).class_name('User') }
    it { should belong_to(:created_by).class_name('User') }
    it { should belong_to(:updated_by).class_name('User') }
  end

  describe 'validations' do
    it { should validate_numericality_of(:completed_percentage).is_greater_than_or_equal_to(0).is_less_than_or_equal_to(100).allow_nil }
    it { should validate_numericality_of(:awarded_value).allow_nil }
    it { should validate_numericality_of(:price).allow_nil }
    it { should validate_numericality_of(:give_price).allow_nil }
    it { should validate_length_of(:awarded_notes).is_at_most(1000).allow_blank }
  end

  it { should define_enum_for(:status).with_values(incomplete: 0, complete: 1, awarded: 2, archived: 3, deleted: 4).backed_by_column_of_type(:integer) }

  describe 'scopes' do
    let(:dealership) { create(:dealership, :without_brand) }
    let(:customer) { create(:customer, dealership: dealership) }
    let(:sales_person) { create(:user) }
    let(:brand) { create(:brand, name: "Test Brand #{SecureRandom.hex(4)}") }
    let!(:appraisal) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person, status: :complete) }
    let!(:customer_vehicle) { create(:customer_vehicle, appraisal: appraisal, brand: brand, rego: 'ABC123') }
    let!(:deleted_appraisal) { create(:appraisal, dealership: dealership, customer: customer, status: :deleted, sales_person: sales_person) }

    it 'excludes deleted appraisals by default' do
      expect(Appraisal.all).to include(appraisal)
      expect(Appraisal.all).not_to include(deleted_appraisal)
    end

    it 'filters by status' do
      expect(Appraisal.filter_by_status(:complete)).to include(appraisal)
    end

    it 'filters by customer' do
      expect(Appraisal.by_customer(customer.uuid)).to include(appraisal)
    end

    it 'filters by brand' do
      expect(Appraisal.by_brand(brand.uuid)).to include(appraisal)
    end

    it 'filters by salesperson' do
      expect(Appraisal.by_salesperson(sales_person.uuid)).to include(appraisal)
    end

    it 'filters by registration number' do
      expect(Appraisal.by_registration_number('ABC123')).to include(appraisal)
    end

    describe 'created_between_dates' do
      it 'filters by start date' do
        start_date = 1.day.ago.strftime('%Y-%m-%d')
        expect(Appraisal.created_between_dates(start_date, nil)).to include(appraisal)
      end

      it 'filters by end date' do
        end_date = 1.day.from_now.strftime('%Y-%m-%d')
        expect(Appraisal.created_between_dates(nil, end_date)).to include(appraisal)
      end

      it 'filters by date range' do
        start_date = 1.day.ago.strftime('%Y-%m-%d')
        end_date = 1.day.from_now.strftime('%Y-%m-%d')
        expect(Appraisal.created_between_dates(start_date, end_date)).to include(appraisal)
      end

      it 'raises error for invalid start date' do
        expect { Appraisal.created_between_dates('invalid-date', nil) }.to raise_error(Errors::InvalidInput, /Invalid date format/)
      end

      it 'raises error for invalid end date' do
        expect { Appraisal.created_between_dates(nil, 'invalid-date') }.to raise_error(Errors::InvalidInput, /Invalid date format/)
      end
    end

    it 'searches by customer name' do
      expect(Appraisal.search_by_term(customer.first_name.downcase)).to include(appraisal)
    end

    it 'searches by customer phone number' do
      customer.update!(phone_number: '+61412345678')
      expect(Appraisal.search_by_term('412345678')).to include(appraisal)
    end

    it 'searches by vehicle build year' do
      expect(Appraisal.search_by_term(customer_vehicle.build_year.to_s)).to include(appraisal)
    end

    it 'rejects short search terms' do
      expect(Appraisal.search_by_term('ab')).to be_empty
    end
  end
end
