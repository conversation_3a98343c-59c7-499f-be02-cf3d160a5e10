require 'rails_helper'

RSpec.describe Customer, type: :model do
  describe 'associations' do
    it { is_expected.to belong_to(:dealership) }
    it { is_expected.to have_many(:drives).dependent(:destroy) }
    it { is_expected.to have_one(:driver_license).dependent(:destroy) }
  end

  it { is_expected.to accept_nested_attributes_for(:driver_license) }

  describe 'validations' do
    subject { build(:customer) }

    it { is_expected.to validate_presence_of(:first_name) }
    it { is_expected.to validate_presence_of(:last_name) }

    context 'email format' do
      it 'allows valid email formats' do
        customer = build(:customer, email: '<EMAIL>')
        expect(customer).to be_valid
      end

      it 'rejects invalid email formats' do
        customer = build(:customer, email: 'invalid-email')
        expect(customer).not_to be_valid
      end
    end

    context 'company name' do
      it 'allows valid company name with length between 3 and 255' do
        customer = build(:customer, company_name: 'Test Company')
        expect(customer).to be_valid
      end

      it 'rejects invalid company name with length less than 3' do
        customer = build(:customer, company_name: 'a')
        expect(customer).not_to be_valid
      end

      it 'rejects invalid company name with length more than 255' do
        customer = build(:customer, company_name: 'a' * 256)
        expect(customer).not_to be_valid
      end

      it 'allows blank company name' do
        customer = build(:customer, company_name: nil)
        expect(customer).to be_valid
      end
    end

    context 'phone number' do
      it 'allows valid phone number with length between 6 and 15' do
        customer = build(:customer, phone_number: '123456')
        expect(customer).to be_valid
      end

      it 'rejects invalid phone number with length less than 6' do
        customer = build(:customer, phone_number: '123')
        expect(customer).not_to be_valid
      end

      it 'rejects invalid phone number with length more than 15' do
        customer = build(:customer, phone_number: '1234567890123456')
        expect(customer).not_to be_valid
      end

      it 'rejects blank phone number' do
        customer = build(:customer, phone_number: nil)
        expect(customer).not_to be_valid
      end
    end

    context 'external id' do
      it 'allows valid external id with length between 1 and 50' do
        customer = build(:customer, external_id: '123456')
        expect(customer).to be_valid
      end

      it 'rejects invalid external id with length more than 50' do
        customer = build(:customer, external_id: 'a' * 51)
        expect(customer).not_to be_valid
      end

      it 'allows blank external id' do
        customer = build(:customer, external_id: nil)
        expect(customer).to be_valid
      end
    end
  end

  describe 'enums' do
    it { is_expected.to define_enum_for(:gender).with_values(unspecified: 0, male: 1, female: 2, other: 3).backed_by_column_of_type(:integer) }
  end

  describe '#full_name' do
    it 'returns the full name' do
      customer = build(:customer, first_name: 'Jane', last_name: 'Smith')
      expect(customer.full_name).to eq('Jane Smith')
    end
  end

  describe '#to_param' do
    it 'returns the uuid' do
      customer = create(:customer)
      expect(customer.to_param).to eq(customer.uuid)
    end
  end

  describe 'email uniqueness validation' do
    let(:dealership) { create(:dealership) }
    let!(:existing_customer) { create(:customer, email: '<EMAIL>', dealership: dealership) }

    it 'allows same email in different dealerships' do
      other_dealership = create(:dealership)
      customer = build(:customer, email: '<EMAIL>', dealership: other_dealership)
      expect(customer).to be_valid
    end

    it 'rejects same email in same dealership' do
      customer = build(:customer, email: '<EMAIL>', dealership: dealership)
      expect(customer).not_to be_valid
      expect(customer.errors[:email]).to include('Customer with this email already exists in this dealership')
    end
  end

  describe 'scopes' do
    let(:dealership) { create(:dealership) }
    let!(:customer1) { create(:customer, first_name: 'John', last_name: 'Doe', email: '<EMAIL>', phone_number: '1234567890', dealership: dealership) }
    let!(:customer2) { create(:customer, first_name: 'Jane', last_name: 'Smith', email: '<EMAIL>', phone_number: '0987654321', dealership: dealership) }
    let!(:customer3) { create(:customer, first_name: 'Bob', last_name: 'Elvis', email: '<EMAIL>', phone_number: '5555555555', dealership: dealership) }

    describe '.search_by_term' do
      it 'finds customers by first name' do
        results = Customer.search_by_term('john')
        expect(results).to include(customer1)
        expect(results).not_to include(customer2, customer3)
      end

      it 'finds customers by last name' do
        results = Customer.search_by_term('smith')
        expect(results).to include(customer2)
        expect(results).not_to include(customer1, customer3)
      end

      it 'finds customers by email' do
        results = Customer.search_by_term('jane@example')
        expect(results).to include(customer2)
        expect(results).not_to include(customer1, customer3)
      end

      it 'finds customers by phone number' do
        results = Customer.search_by_term('555555')
        expect(results).to include(customer3)
        expect(results).not_to include(customer1, customer2)
      end

      it 'is case insensitive' do
        results = Customer.search_by_term('JOHN')
        expect(results).to include(customer1)
      end

      it 'returns none for blank term' do
        expect(Customer.search_by_term('')).to be_empty
        expect(Customer.search_by_term(nil)).to be_empty
        expect(Customer.search_by_term('   ')).to be_empty
      end

      it 'returns none for terms less than 3 characters' do
        expect(Customer.search_by_term('jo')).to be_empty
      end

      it 'returns none for terms more than 20 characters' do
        long_term = 'a' * 21
        expect(Customer.search_by_term(long_term)).to be_empty
      end

      it 'respects the limit parameter' do
        # Create more customers to test limit
        create_list(:customer, 25, dealership: dealership, first_name: 'Test')
        results = Customer.search_by_term('test', limit: 10)
        expect(results.count).to eq(10)
      end

      it 'orders results by first_name and last_name' do
        create(:customer, first_name: 'Alice', last_name: 'Zulu', dealership: dealership)
        create(:customer, first_name: 'Alice', last_name: 'Alpha', dealership: dealership)

        results = Customer.search_by_term('alice')
        expect(results.first.last_name).to eq('Alpha')
        expect(results.last.last_name).to eq('Zulu')
      end
    end

    describe '.ordered_by_name' do
      it 'orders customers by first name then last name' do
        results = Customer.ordered_by_name
        expect(results.first).to eq(customer3) # Bob Johnson
        expect(results.second).to eq(customer2) # Jane Smith
        expect(results.third).to eq(customer1) # John Doe
      end
    end
  end
end
