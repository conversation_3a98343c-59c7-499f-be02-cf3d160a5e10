# frozen_string_literal: true

require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::Dealerships::DrivesController", type: :request do
  include_context "drive_api_shared_context"

  let!(:vehicle1) { create(:vehicle, dealership: dealership, rego: "ABC123", stock_number: "T001", make: "Toyota", model: "Camry", build_year: 2023, color: "Red") }
  let!(:vehicle2) { create(:vehicle, dealership: dealership, rego: nil, stock_number: "H002", make: "Honda", model: "Civic", build_year: 2022, color: "Blue") }
  let!(:customer1) { create(:customer, dealership: dealership, first_name: "<PERSON>", last_name: "<PERSON><PERSON>", email: "<EMAIL>", phone_number: "+61412345678") }
  let!(:customer2) { create(:customer, dealership: dealership, first_name: "<PERSON>", last_name: "<PERSON>", email: "<EMAIL>", phone_number: "+61487654321") }
  let!(:trade_plate) { create(:trade_plate, dealership: dealership) }
  let!(:sales_person2) { create(:user) }
  let!(:sales_person_dealership2) { create(:user_dealership, user: sales_person2, dealership: dealership, role: :sales_person) }

  let!(:drive1) do
    create(:drive,
           dealership: dealership,
           vehicle: vehicle1,
           customer: customer1,
           sales_person: sales_person,
           trade_plate: trade_plate,
           drive_type: :test_drive,
           status: :completed,
           sold_status: :sold,
           start_datetime: 2.days.ago)
  end

  let!(:drive2) do
    create(:drive,
           dealership: dealership,
           vehicle: vehicle2,
           customer: customer2,
           sales_person: sales_person,
           drive_type: :loan,
           status: :in_progress,
           sold_status: :unsold,
           start_datetime: 1.day.ago, expected_return_datetime: 2.hours.ago)
  end

  let!(:drive3) do
    create(:drive,
           dealership: dealership,
           vehicle: vehicle1,
           customer: customer1,
           sales_person: sales_person,
           drive_type: :enquiry,
           status: :scheduled,
           start_datetime: Time.current)
  end

  let!(:drive4) do
    create(:drive,
           dealership: dealership,
           vehicle: vehicle2,
           customer: customer2,
           sales_person: sales_person,
           drive_type: :test_drive_booking,
           status: :scheduled,
           expected_pickup_datetime: 1.day.from_now,
           expected_return_datetime: 26.hours.from_now,
           updated_at: 2.days.ago)
  end

  let!(:drive5) do
    create(:drive,
           dealership: dealership,
           vehicle: vehicle1,
           customer: customer1,
           sales_person: sales_person2,
           drive_type: :loan_booking,
           status: :scheduled,
           expected_pickup_datetime: 1.day.from_now,
           expected_return_datetime: 2.days.from_now,
           updated_at: 1.day.ago)
  end

  let!(:deleted_drive) { create(:drive, drive_type: :test_drive, status: :deleted, dealership: dealership, sales_person: sales_person) }

  describe "GET /api/v1/dealerships/:dealership_uuid/drives" do
    subject { get "/api/v1/dealerships/#{dealership.uuid}/drives", headers: headers, params: params }
    let(:params) { {} }

    context "with valid authentication" do
      it "returns all drives with pagination" do
        subject
        expect(response).to have_http_status(:ok)

        json = response.parsed_body
        expect(json.dig("status", "message")).to eq("Drives retrieved successfully")

        drives = json.dig("data", "drives")
        expect(drives.size).to eq(5)

        expect(drives[0]["uuid"]).to eq(drive3.uuid)
        expect(drives[1]["uuid"]).to eq(drive2.uuid)
        expect(drives[2]["uuid"]).to eq(drive1.uuid)
      end

      it "includes pagination headers" do
        subject

        expect(response.headers['X-Current-Page']).to eq('1')
        expect(response.headers['X-Per-Page']).to eq('20')
        expect(response.headers['X-Total-Count']).to eq('5')
        expect(response.headers['X-Total-Pages']).to eq('1')
      end

      it "includes all drive attributes and associations" do
        subject

        json = response.parsed_body
        drive_data = json.dig("data", "drives").first

        expect(drive_data).to include("uuid", "drive_type", "status", "sold_status")
        expect(drive_data).to include("start_datetime", "end_datetime")
        expect(drive_data).to include("created_at", "updated_at")

        expect(drive_data["vehicle"]).to include("uuid", "make", "model", "build_year", "display_name")
        expect(drive_data["customer"]).to include("uuid", "first_name", "last_name", "full_name")
        expect(drive_data["sales_person"]).to include("uuid", "first_name", "last_name", "full_name")
      end

      context "with vehicle_uuid filter" do
        let(:params) { { vehicle_uuid: vehicle1.uuid } }

        it "returns only drives for the specified vehicle" do
          subject

          json = response.parsed_body
          drives = json.dig("data", "drives")
          expect(drives.size).to eq(3)
          drives.each do |drive|
            expect(drive.dig("vehicle", "uuid")).to eq(vehicle1.uuid)
          end
        end
      end

      context "with customer_uuid filter" do
        let(:params) { { customer_uuid: customer2.uuid } }

        it "returns only drives for the specified customer" do
          subject

          json = response.parsed_body
          drives = json.dig("data", "drives")
          expect(drives.size).to eq(2)
          expect(drives.first.dig("customer", "uuid")).to eq(customer2.uuid)
        end
      end

      context "with drive_type filter" do
        let(:params) { { drive_type: "test_drive" } }

        it "returns only drives of the specified type" do
          subject

          json = response.parsed_body
          drives = json.dig("data", "drives")
          expect(drives.size).to eq(1)
          expect(drives.first["drive_type"]).to eq("test_drive")
        end
      end

      context "with status filter" do
        let(:params) { { status: "completed" } }

        it "returns only drives with the specified status" do
          subject

          json = response.parsed_body
          drives = json.dig("data", "drives")
          expect(drives.size).to eq(1)
          expect(drives.first["status"]).to eq("completed")
        end
      end

      context "with sold_status filter" do
        let(:params) { { sold_status: "sold" } }

        it "returns only drives with the specified sold status" do
          subject

          json = response.parsed_body
          drives = json.dig("data", "drives")
          expect(drives.size).to eq(1)
          expect(drives.first["sold_status"]).to eq("sold")
        end
      end

      context "with sales_person_uuid filter" do
        let(:params) { { sales_person_uuid: sales_person.uuid } }

        it "returns only drives for the specified sales person" do
          subject

          json = response.parsed_body
          drives = json.dig("data", "drives")
          expect(drives.size).to eq(4)
          drives.each do |drive|
            expect(drive.dig("sales_person", "uuid")).to eq(sales_person.uuid)
          end
        end
      end

      context "with overdue filter" do
        let(:params) { { overdue: true } }
        it "returns only overdue drives" do
          subject

          json = response.parsed_body
          drives = json.dig("data", "drives")
          expect(drives.size).to eq(1)
          expect(drives.first["uuid"]).to eq(drive2.uuid)
        end
      end

      context "with updated_at date range filter" do
        let(:params) { { updated_from: 2.days.ago.to_date.to_s, updated_to: 1.day.ago.to_date.to_s } }
        it "returns only drives updated within the specified date range" do
          subject

          json = response.parsed_body
          drives = json.dig("data", "drives")
          expect(drives.size).to eq(2)
          expect(drives.first["uuid"]).to eq(drive5.uuid)
        end
      end

      context "with eligible_for_return filter" do
        let(:params) { { eligible_for_return: true } }
        it "returns only drives eligible for return" do
          subject

          json = response.parsed_body
          drives = json.dig("data", "drives")
          expect(drives.size).to eq(1)
          expect(drives.first["uuid"]).to eq(drive2.uuid)
        end
      end

      context "with trade_plate_uuid filter" do
        let(:params) { { trade_plate_uuid: trade_plate.uuid } }

        it "returns only drives using the specified trade plate" do
          subject

          json = response.parsed_body
          drives = json.dig("data", "drives")
          expect(drives.size).to eq(1)
          expect(drives.first.dig("trade_plate", "uuid")).to eq(trade_plate.uuid)
        end
      end

      context "with start_datetime date range filter" do
        let(:params) { { start_date_from: 1.day.ago.to_date.to_s, start_date_to: Date.current.to_s } }

        it "returns only drives within the specified date range" do
          subject

          json = response.parsed_body
          drives = json.dig("data", "drives")
          expect(drives.size).to eq(4)
        end
      end

      context "with search query" do
        let(:params) { { query: "ane" } }

        it "returns drives matching the search query" do
          subject

          json = response.parsed_body
          drives = json.dig("data", "drives")
          expect(drives.size).to eq(2)
          expect(drives.first.dig("customer", "full_name")).to eq("Jane Smith")
        end
      end

      context "with search query for vehicle" do
        let(:params) { { query: "Civ" } }

        it "returns drives matching the search query for vehicle" do
          subject

          json = response.parsed_body
          drives = json.dig("data", "drives")
          expect(drives.size).to eq(2)
          expect(drives.first.dig("vehicle", "model")).to eq("Civic")
        end
      end

      context "with multiple filters" do
        let(:params) { { vehicle_uuid: vehicle1.uuid, status: "scheduled" } }

        it "applies all filters correctly" do
          subject

          json = response.parsed_body
          drives = json.dig("data", "drives")
          expect(drives.size).to eq(2)
          expect(drives.first["uuid"]).to eq(drive3.uuid)
        end
      end

      context "with filter and search query" do
        let(:params) { { query: "022", drive_type: :test_drive_booking } }

        it "applies all filters correctly" do
          subject

          json = response.parsed_body
          drives = json.dig("data", "drives")
          expect(drives.size).to eq(1)
          expect(drives.first["uuid"]).to eq(drive4.uuid)
        end
      end

      context "with pagination" do
        let(:params) { { per_page: 2, page: 1 } }

        it "respects pagination parameters" do
          subject

          json = response.parsed_body
          drives = json.dig("data", "drives")
          expect(drives.size).to eq(2)

          expect(response.headers['X-Current-Page']).to eq('1')
          expect(response.headers['X-Per-Page']).to eq('2')
          expect(response.headers['X-Total-Count']).to eq('5')
          expect(response.headers['X-Total-Pages']).to eq('3')
        end
      end
    end

    context "with invalid filters" do
      context "invalid drive_type" do
        let(:params) { { drive_type: "invalid_type" } }

        it "returns empty results" do
          subject
          expect(response).to have_http_status(:ok)

          json = response.parsed_body
          expect(json.dig("data", "drives")).to be_empty
        end
      end

      context "invalid status" do
        let(:params) { { status: "invalid_status" } }

        it "returns empty results" do
          subject
          expect(response).to have_http_status(:ok)

          json = response.parsed_body
          expect(json.dig("data", "drives")).to be_empty
        end
      end

      context "invalid date format for start_date_from" do
        let(:params) { { start_date_from: "invalid-date" } }

        it "returns validation error" do
          subject
          expect(response).to have_http_status(:unprocessable_content)

          json = response.parsed_body
          expect(json.dig("status", "message")).to include("Invalid date format")
        end
      end

      context "invalid date format for start_date_to" do
        let(:params) { { start_date_to: "invalid-date" } }

        it "returns validation error" do
          subject
          expect(response).to have_http_status(:unprocessable_content)

          json = response.parsed_body
          expect(json.dig("status", "message")).to include("Invalid date format")
        end
      end

      context "invalid date format for updated_from" do
        let(:params) { { updated_from: "invalid-date" } }

        it "returns validation error" do
          subject
          expect(response).to have_http_status(:unprocessable_content)

          json = response.parsed_body
          expect(json.dig("status", "message")).to include("Invalid date format")
        end
      end

      context "invalid date format for updated_to" do
        let(:params) { { updated_to: "invalid-date" } }

        it "returns validation error" do
          subject
          expect(response).to have_http_status(:unprocessable_content)

          json = response.parsed_body
          expect(json.dig("status", "message")).to include("Invalid date format")
        end
      end

      context "invalid sold_status" do
        let(:params) { { sold_status: "invalid_status" } }

        it "returns empty results" do
          subject
          expect(response).to have_http_status(:ok)

          json = response.parsed_body
          expect(json.dig("data", "drives")).to be_empty
        end
      end

      context "non-existent vehicle_uuid" do
        let(:params) { { vehicle_uuid: "non-existent-uuid" } }

        it "returns empty results" do
          subject
          expect(response).to have_http_status(:ok)

          json = response.parsed_body
          expect(json.dig("data", "drives")).to be_empty
        end
      end
    end

    context "without authentication" do
      let(:headers) { {} }

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  path '/api/v1/dealerships/{dealership_uuid}/drives' do
    parameter name: 'dealership_uuid', in: :path, type: :string, description: 'Dealership UUID'
    get('List drives for a dealership') do
      tags 'Drives'
      description 'Retrieves all drives for a specific dealership with pagination and filtering options. Results are ordered by start_datetime descending.'
      operationId 'getDrives'
      consumes 'application/json'
      produces 'application/json'

      parameter name: 'Authorization', in: :header, type: :string, required: true, description: 'Bearer token for authentication'
      parameter name: 'Device-ID', in: :header, type: :string, required: true, description: 'Device ID for the request'

      parameter name: 'page', in: :query, type: :integer, required: false, description: 'Page number (default: 1)'
      parameter name: 'per_page', in: :query, type: :integer, required: false, description: 'Items per page (default: 20, max: 100)'

      parameter name: 'vehicle_uuid', in: :query, type: :string, required: false, description: 'Filter by vehicle UUID'
      parameter name: 'customer_uuid', in: :query, type: :string, required: false, description: 'Filter by customer UUID'
      parameter name: 'drive_type', in: :query, required: false, description: 'Filter by drive type', schema: {
        type: :string,
        enum: [ 'test_drive', 'enquiry', 'loan', 'loan_booking', 'test_drive_booking', 'self_loan' ]
      }
      parameter name: 'sales_person_uuid', in: :query, type: :string, required: false, description: 'Filter by sales person UUID'
      parameter name: 'status', in: :query, required: false, description: 'Filter by drive status', schema: {
        type: :string,
        enum: [ 'scheduled', 'in_progress', 'completed', 'cancelled', 'draft', 'deleted' ]
      }
      parameter name: 'sold_status', in: :query, required: false, description: 'Filter by sold status', schema: {
        type: :string,
        enum: [ 'unsold', 'sold' ]
      }
      parameter name: 'trade_plate_uuid', in: :query, type: :string, required: false, description: 'Filter by trade plate UUID'
      parameter name: 'start_date_from', in: :query, type: :string, format: :date, required: false, description: 'Filter by start date from (YYYY-MM-DD)'
      parameter name: 'start_date_to', in: :query, type: :string, format: :date, required: false, description: 'Filter by start date to (YYYY-MM-DD)'
      parameter name: 'overdue', in: :query, type: :boolean, required: false, description: 'Filter by overdue drives'
      parameter name: 'updated_from', in: :query, type: :string, format: :date, required: false, description: 'Filter by updated from (YYYY-MM-DD)'
      parameter name: 'updated_to', in: :query, type: :string, format: :date, required: false, description: 'Filter by updated to (YYYY-MM-DD)'
      parameter name: 'eligible_for_return', in: :query, type: :boolean, required: false, description: 'Filter by eligible for return'
      parameter name: 'query', in: :query, type: :string, required: false, description: 'Search drives by customer or vehicle details (minimum 3 characters)'

      response(200, 'successful') do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 200 },
                     message: { type: :string, example: 'Drives retrieved successfully' }
                   },
                   required: [ 'code', 'message' ]
                 },
                 data: {
                   type: :object,
                   properties: {
                     drives: {
                       type: :array,
                       items: {
                         type: :object,
                         properties: {
                           uuid: { type: :string, example: '550e8400-e29b-41d4-a716-************' },
                           drive_type: { type: :string, enum: [ 'test_drive', 'enquiry', 'loan', 'loan_booking', 'test_drive_booking', 'self_loan' ], example: 'test_drive' },
                           status: { type: :string, enum: [ 'scheduled', 'in_progress', 'completed', 'cancelled', 'draft', 'deleted' ], example: 'completed' },
                           sold_status: { type: :string, enum: [ 'unsold', 'sold' ], example: 'sold' },
                           notes: { type: :string, example: 'Customer interested in purchasing', nullable: true },
                           expected_pickup_datetime: { type: :string, format: 'date-time', example: '2023-07-01T10:00:00Z', nullable: true },
                           expected_return_datetime: { type: :string, format: 'date-time', example: '2023-07-01T12:00:00Z', nullable: true },
                           start_datetime: { type: :string, format: 'date-time', example: '2023-07-01T10:15:00Z', nullable: true },
                           end_datetime: { type: :string, format: 'date-time', example: '2023-07-01T11:45:00Z', nullable: true },
                           start_odometer_reading: { type: :integer, example: 15000, nullable: true },
                           end_odometer_reading: { type: :integer, example: 15025, nullable: true },
                           vehicle: {
                             type: :object,
                             properties: {
                               uuid: { type: :string, example: '550e8400-e29b-41d4-a716-************' },
                               make: { type: :string, example: 'Toyota' },
                               model: { type: :string, example: 'Camry' },
                               build_year: { type: :integer, example: 2023 },
                               color: { type: :string, example: 'Blue' },
                               rego: { type: :string, example: 'ABC123' },
                               display_name: { type: :string, example: '2023 Toyota Camry' }
                             }
                           },
                           customer: {
                             type: :object,
                             nullable: true,
                             properties: {
                               uuid: { type: :string, example: '550e8400-e29b-41d4-a716-************' },
                               first_name: { type: :string, example: 'John' },
                               last_name: { type: :string, example: 'Doe' },
                               full_name: { type: :string, example: 'John Doe' },
                               email: { type: :string, example: '<EMAIL>' },
                               phone_number: { type: :string, example: '+61412345678' }
                             }
                           },
                           sales_person: {
                             type: :object,
                             properties: {
                               uuid: { type: :string, example: '550e8400-e29b-41d4-a716-************' },
                               first_name: { type: :string, example: 'Jane' },
                               last_name: { type: :string, example: 'Smith' },
                               full_name: { type: :string, example: 'Jane Smith' },
                               email: { type: :string, example: '<EMAIL>' }
                             }
                           },
                           sales_person_accompanying: {
                             type: :object,
                             nullable: true,
                             properties: {
                               uuid: { type: :string, example: '550e8400-e29b-41d4-a716-************' },
                               first_name: { type: :string, example: 'Bob' },
                               last_name: { type: :string, example: 'Wilson' },
                               full_name: { type: :string, example: 'Bob Wilson' },
                               email: { type: :string, example: '<EMAIL>' }
                             }
                           },
                           trade_plate: {
                             type: :object,
                             nullable: true,
                             properties: {
                               uuid: { type: :string, example: '550e8400-e29b-41d4-a716-************' },
                               number: { type: :string, example: 'TP001' },
                               expiry: { type: :string, format: :date, example: '2024-12-31' },
                               status: { type: :string, enum: [ 'active', 'inactive' ], example: 'active' }
                             }
                           },
                           driver_license: {
                             type: :object,
                             nullable: true,
                             properties: {
                               uuid: { type: :string, example: '550e8400-e29b-41d4-a716-************' },
                               licence_number: { type: :string, example: 'DL123456789' },
                               full_name: { type: :string, example: 'John Doe' },
                               expiry_date: { type: :string, format: :date, example: '2025-06-30' }
                             }
                           },
                           created_at: { type: :string, format: 'date-time', example: '2023-07-01T09:00:00Z' },
                           updated_at: { type: :string, format: 'date-time', example: '2023-07-01T12:00:00Z' }
                         }
                       }
                     }
                   },
                   required: [ 'drives' ]
                 }
               },
               required: [ 'status', 'data' ]

        header 'X-Current-Page', schema: { type: :string }, description: 'Current page number'
        header 'X-Per-Page', schema: { type: :string }, description: 'Items per page'
        header 'X-Total-Count', schema: { type: :string }, description: 'Total number of items'
        header 'X-Total-Pages', schema: { type: :string }, description: 'Total number of pages'
        run_test!
      end

      response(401, 'unauthorized') do
        let(:Authorization) { 'Bearer invalid_token' }
        let(:'Device-ID') { 'invalid_device_id' }
        run_test!
      end

      response(404, 'dealership not found') do
        let(:dealership_uuid) { 'non-existent-uuid' }
        run_test!
      end

      response(200, 'empty results for invalid filter') do
        let(:drive_type) { 'invalid_type' }
        run_test!
      end
    end
  end
end
