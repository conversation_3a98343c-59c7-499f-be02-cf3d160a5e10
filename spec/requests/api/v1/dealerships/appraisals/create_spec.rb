# frozen_string_literal: true

require 'rails_helper'
require 'swagger_helper'

RSpec.describe "POST /api/v1/dealerships/{dealership_uuid}/appraisals", type: :request do
  include_context "users_api_shared_context"

  let(:dealership) { create(:dealership, :without_dealership_group, :without_brand) }
  let(:customer) { create(:customer, dealership: dealership) }
  let!(:user_dealership) { create(:user_dealership, user: user, dealership: dealership, role: :sales_person) }

  path "/api/v1/dealerships/{dealership_uuid}/appraisals" do
    post "Create a new appraisal" do
      tags "Appraisals"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :payload, in: :body, required: true, schema: {
        type: :object,
        required: %w[customer_uuid],
        properties: {
          customer_uuid: { type: :string, format: :uuid, example: "550e8400-e29b-41d4-a716-************", description: "Customer UUID" }
        }
      }

      response "201", "Appraisal created successfully" do
         schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 201 },
                     message: { type: :string, example: 'Appraisal Created successfully' }
                   },
                   required: [ 'code', 'message' ]
                 },
                 data: {
                   type: :object,
                   properties: {
                     appraisal: {
                       type: :object,
                       properties: {
                         uuid: { type: :string, example: '550e8400-e29b-41d4-a716-************' },
                         status: { type: :string, enum: [ 'incomplete' ], example: 'incomplete' },
                         vehicle: {
                           type: :object,
                           nullable: true,
                           properties: {
                             uuid: { type: :string, example: '550e8400-e29b-41d4-a716-************' },
                             make: { type: :string, example: 'Toyota' },
                             model: { type: :string, example: 'Camry' },
                             build_year: { type: :integer, example: 2023 },
                             color: { type: :string, example: 'White' },
                             rego: { type: :string, example: 'ABC123' },
                             display_name: { type: :string, example: '2023 Toyota Camry' }
                           }
                         },
                         customer: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, example: '550e8400-e29b-41d4-a716-************' },
                             first_name: { type: :string, example: 'John' },
                             last_name: { type: :string, example: 'Doe' },
                             email: { type: :string, example: '<EMAIL>' },
                             phone_number: { type: :string, example: '+61400000000' },
                             full_name: { type: :string, example: 'John Doe' }
                           }
                         },
                         sales_person: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, example: '550e8400-e29b-41d4-a716-************' },
                             first_name: { type: :string, example: 'Jane' },
                             last_name: { type: :string, example: 'Smith' },
                             email: { type: :string, example: '<EMAIL>' },
                             full_name: { type: :string, example: 'Jane Smith' }
                           }
                         },
                         updated_by: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, example: '550e8400-e29b-41d4-a716-************' },
                             first_name: { type: :string, example: 'Bob' },
                             last_name: { type: :string, example: 'Johnson' },
                             email: { type: :string, example: '<EMAIL>' },
                             full_name: { type: :string, example: 'Bob Johnson' }
                           }
                         },
                         created_by: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, example: '550e8400-e29b-41d4-a716-************' },
                             first_name: { type: :string, example: 'Bob' },
                             last_name: { type: :string, example: 'Johnson' },
                             email: { type: :string, example: '<EMAIL>' },
                             full_name: { type: :string, example: 'Bob Johnson' }
                           }
                         },
                         created_at: { type: :string, format: 'date-time', example: '2023-07-01T09:00:00Z' },
                         updated_at: { type: :string, format: 'date-time', example: '2023-07-01T12:00:00Z' }
                       }
                     }
                   },
                   required: [ 'appraisal' ]
                 }
               },
               required: [ 'status', 'data' ]

        let(:dealership_uuid) { dealership.uuid }
        let(:payload) do
          {
            customer_uuid: customer.uuid
          }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(201)
          expect(json.dig("status", "message")).to eq("Appraisal created successfully")
          expect(json.dig("data", "appraisal")).to be_present
          expect(json.dig("data", "appraisal", "status")).to eq("incomplete")
          expect(json.dig("data", "appraisal", "uuid")).to be_present
          expect(json.dig("data", "appraisal", "customer")).to be_present
          expect(json.dig("data", "appraisal", "customer", "uuid")).to eq(customer.uuid)
          expect(json.dig("data", "appraisal", "sales_person")).to be_present
          expect(json.dig("data", "appraisal", "sales_person", "uuid")).to eq(user.uuid)
          expect(json.dig("data", "appraisal", "created_by")).to be_present
          expect(json.dig("data", "appraisal", "created_by", "uuid")).to eq(user.uuid)
          expect(json.dig("data", "appraisal", "updated_by")).to be_present
          expect(json.dig("data", "appraisal", "updated_by", "uuid")).to eq(user.uuid)
          expect(json.dig("data", "appraisal", "created_at")).to be_present
          expect(json.dig("data", "appraisal", "updated_at")).to be_present
        end
      end

      response "422", "Missing customer_uuid" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 422 },
                     message: { type: :string, example: "customer_uuid is required" }
                   }
                 }
               },
               required: [ 'status' ]
        let(:dealership_uuid) { dealership.uuid }
        let(:payload) { {} }

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(422)
          expect(json.dig("status", "message")).to eq("customer_uuid is required")
        end
      end

      response "404", "Customer not found" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 404 },
                     message: { type: :string, example: "Customer not found or does not belong to this dealership" }
                   }
                 }
               },
               required: [ 'status' ]
        let(:dealership_uuid) { dealership.uuid }
        let(:payload) do
          {
            customer_uuid: "invalid-uuid"
          }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(404)
          expect(json.dig("status", "message")).to eq("Customer not found or does not belong to this dealership")
        end
      end

      response "404", "Dealership not found" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 404 },
                     message: { type: :string, example: "Dealership not found or you don't have access to it" }
                   }
                 }
               },
               required: [ 'status' ]
        let(:dealership_uuid) { "invalid-uuid" }
        let(:payload) do
          {
            customer_uuid: customer.uuid
          }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(404)
          expect(json.dig("status", "message")).to eq("Dealership not found or you don't have access to it")
        end
      end

      response "401", "Unauthorized" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 401 },
                     message: { type: :string, example: "Missing authorization token" }
                   }
                 }
               },
               required: [ 'status' ]
        let(:Authorization) { nil }
        let(:dealership_uuid) { dealership.uuid }
        let(:payload) do
          {
            customer_uuid: customer.uuid
          }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(401)
        end
      end
    end
  end

  describe "POST /api/v1/dealerships/{dealership_uuid}/appraisals" do
    subject { post "/api/v1/dealerships/#{dealership.uuid}/appraisals", params: params, headers: headers }

    context "with valid parameters" do
      let(:params) do
        {
          customer_uuid: customer.uuid
        }
      end

      it "creates a new appraisal" do
        expect {
          subject
        }.to change(Appraisal, :count).by(1)
      end

      it "sets the correct attributes" do
        subject

        appraisal = Appraisal.last
        expect(appraisal.dealership).to eq(dealership)
        expect(appraisal.customer).to eq(customer)
        expect(appraisal.sales_person).to eq(user)
        expect(appraisal.created_by).to eq(user)
        expect(appraisal.updated_by).to eq(user)
        expect(appraisal.status).to eq("incomplete")
      end

      it "returns 201 status" do
        subject
        expect(response).to have_http_status(:created)
      end

      it "returns the correct JSON structure" do
        subject

        json_response = response.parsed_body
        expect(json_response["status"]["code"]).to eq(201)
        expect(json_response["status"]["message"]).to eq("Appraisal created successfully")
        expect(json_response["data"]["appraisal"]).to be_present
      end
    end

    context "with missing customer_uuid" do
      let(:params) { {} }

      it "returns 422 status" do
        subject
        expect(response).to have_http_status(:unprocessable_content)
      end

      it "returns error message" do
        subject

        json_response = response.parsed_body
        expect(json_response["status"]["code"]).to eq(422)
        expect(json_response["status"]["message"]).to eq("customer_uuid is required")
      end
    end

    context "with invalid customer_uuid" do
      let(:params) do
        {
          customer_uuid: "invalid-uuid"
        }
      end

      it "returns 404 status" do
        subject
        expect(response).to have_http_status(:not_found)
      end

      it "returns error message" do
        subject

        json_response = response.parsed_body
        expect(json_response["status"]["code"]).to eq(404)
        expect(json_response["status"]["message"]).to eq("Customer not found or does not belong to this dealership")
      end
    end

    context "with invalid dealership_uuid" do
      subject { post "/api/v1/dealerships/invalid-uuid/appraisals", params: params, headers: headers }

      let(:params) do
        {
          customer_uuid: customer.uuid
        }
      end

      it "returns 404 status" do
        subject
        expect(response).to have_http_status(:not_found)
      end

      it "returns error message" do
        subject

        json_response = response.parsed_body
        expect(json_response["status"]["code"]).to eq(404)
        expect(json_response["status"]["message"]).to eq("Dealership not found or you don't have access to it")
      end
    end
  end
end
