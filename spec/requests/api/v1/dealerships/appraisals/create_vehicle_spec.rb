# frozen_string_literal: true

require 'rails_helper'
require 'swagger_helper'

RSpec.describe "POST /api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/vehicle", type: :request do
  include_context "users_api_shared_context"

  let(:dealership) { create(:dealership, :without_dealership_group, :without_brand) }
  let(:customer) { create(:customer, dealership: dealership) }
  let!(:user_dealership) { create(:user_dealership, user: user, dealership: dealership, role: :sales_person) }
  let(:appraisal) { create(:appraisal, dealership: dealership, customer: customer, sales_person: user) }

  path "/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/vehicle" do
    post "Create vehicle for appraisal" do
      tags "Appraisals"
      consumes "multipart/form-data"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :appraisal_uuid, in: :path, type: :string, required: true, description: "Appraisal UUID"

      parameter name: :vehicle, in: :formData, schema: {
        type: :object,
        properties: {
          make: { type: :string, description: "Vehicle make", example: "Toyota" },
          model: { type: :string, description: "Vehicle model", example: "Camry" },
          build_year: { type: :integer, description: "Vehicle build year", example: 2020 },
          vin: { type: :string, description: "Vehicle identification number", example: "1HGBH41JXMN109186" },
          rego: { type: :string, description: "Registration number", example: "ABC123" },
          registration_state: { type: :string, description: "Registration state", example: "NSW" },
          registration_expiry: { type: :string, format: :date, description: "Registration expiry date" },
          build_month: { type: :integer, description: "Vehicle build month (1-12)" },
          compliance_year: { type: :integer, description: "Compliance year" },
          compliance_month: { type: :integer, description: "Compliance month (1-12)" },
          exterior_color: { type: :string, description: "Exterior color", example: "White" },
          interior_color: { type: :string, description: "Interior color", example: "Black" },
          engine_kilowatts: { type: :integer, description: "Engine power in kilowatts", example: 150 },
          engine_number: { type: :string, description: "Engine number" },
          wheel_size_front: { type: :integer, description: "Front wheel size in inches", example: 17 },
          wheel_size_rear: { type: :integer, description: "Rear wheel size in inches", example: 17 },
          odometer_reading: { type: :integer, description: "Odometer reading", example: 50000 },
          odometer_date: { type: :string, format: :date, description: "Odometer reading date" },
          redbook_code: { type: :string, description: "Redbook code" },
          seat_type: { type: :string, enum: [ "leather", "cloth", "mixed" ], description: "Seat type" },
          fuel_type: { type: :string, enum: [ "petrol", "diesel", "electric", "hybrid", "plugin_hybrid", "lpg", "other" ], description: "Fuel type" },
          driving_wheels: { type: :string, enum: [ "fwd", "rwd", "awd", "four_wd" ], description: "Driving wheels" },
          spare_wheel_type: { type: :string, enum: [ "full_size", "space_saver", "run_flat", "repair_kit", "no_spare_wheel" ], description: "Spare wheel type" },
          transmission: { type: :string, enum: [ "manual", "automatic", "cvt", "semi_automatic", "dual_clutch" ], description: "Transmission type" },
          body_type: { type: :string, enum: [ "sedan", "hatchback", "wagon", "suv", "coupe", "convertible", "ute", "van", "truck", "unknown_body" ], description: "Body type" },
          number_of_doors: { type: :integer, description: "Number of doors", example: 4 },
          number_of_seats: { type: :integer, description: "Number of seats", example: 5 },
          is_vehicle_present: { type: :boolean, description: "Is vehicle present for inspection" },
          main_photo: { type: :string, format: :binary, description: "Main vehicle photo" },
          odometer_reading_photo: { type: :string, format: :binary, description: "Odometer reading photo" },
          photos: { type: :array, items: { type: :string, format: :binary }, description: "Multiple vehicle photos (max 5)", maxItems: 5 }
        },
        required: [ 'make', 'model', 'build_year' ]
      }

      response "201", "Vehicle created successfully" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 201 },
                     message: { type: :string, example: 'Vehicle created successfully' }
                   },
                   required: [ 'code', 'message' ]
                 },
                 data: {
                   type: :object,
                   properties: {
                     appraisal: {
                       type: :object,
                       properties: {
                         uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                         status: { type: :string, enum: [ 'incomplete', 'complete', 'archived', 'deleted' ], example: 'incomplete' },
                         customer: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                             first_name: { type: :string, example: 'John' },
                             last_name: { type: :string, example: 'Doe' },
                             email: { type: :string, example: '<EMAIL>' },
                             phone_number: { type: :string, example: '+61400000000' },
                             full_name: { type: :string, example: 'John Doe' }
                           }
                         },
                         sales_person: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                             first_name: { type: :string, example: 'Jane' },
                             last_name: { type: :string, example: 'Smith' },
                             email: { type: :string, example: '<EMAIL>' },
                             full_name: { type: :string, example: 'Jane Smith' },
                             phone: { type: :string, example: '+61400000001' },
                             job_title: { type: :string, nullable: true, example: 'Sales Representative' }
                           }
                         },
                         created_by: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                             first_name: { type: :string, example: 'Bob' },
                             last_name: { type: :string, example: 'Johnson' },
                             email: { type: :string, example: '<EMAIL>' },
                             full_name: { type: :string, example: 'Bob Johnson' },
                             phone: { type: :string, example: '+61400000002' },
                             job_title: { type: :string, nullable: true, example: 'Sales Manager' }
                           }
                         },
                         updated_by: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                             first_name: { type: :string, example: 'Alice' },
                             last_name: { type: :string, example: 'Brown' },
                             email: { type: :string, example: '<EMAIL>' },
                             full_name: { type: :string, example: 'Alice Brown' },
                             phone: { type: :string, example: '+61400000003' },
                             job_title: { type: :string, nullable: true, example: 'Sales Representative' }
                           }
                         },
                         vehicle: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                             make: { type: :string, example: 'Toyota' },
                             model: { type: :string, example: 'Camry' },
                             vin: { type: :string, example: '1HGBH41JXMN109186' },
                             rego: { type: :string, example: 'ABC123' },
                             registration_expiry: { type: :string, format: :date, nullable: true },
                             registration_state: { type: :string, example: 'NSW' },
                             build_year: { type: :integer, example: 2020 },
                             build_month: { type: :integer, nullable: true },
                             compliance_year: { type: :integer, nullable: true },
                             compliance_month: { type: :integer, nullable: true },
                             exterior_color: { type: :string, example: 'White' },
                             interior_color: { type: :string, example: 'Black' },
                             seat_type: { type: :string, enum: [ 'leather', 'cloth', 'mixed' ], example: 'leather' },
                             fuel_type: { type: :string, enum: [ 'petrol', 'diesel', 'electric', 'hybrid', 'plugin_hybrid', 'lpg', 'other' ], example: 'petrol' },
                             driving_wheels: { type: :string, enum: [ 'fwd', 'rwd', 'awd', 'four_wd' ], example: 'fwd' },
                             spare_wheel_type: { type: :string, enum: [ 'full_size', 'space_saver', 'run_flat', 'repair_kit', 'no_spare_wheel' ], example: 'full_size' },
                             transmission: { type: :string, enum: [ 'manual', 'automatic', 'cvt', 'semi_automatic', 'dual_clutch' ], example: 'automatic' },
                             body_type: { type: :string, enum: [ 'sedan', 'hatchback', 'wagon', 'suv', 'coupe', 'convertible', 'ute', 'van', 'truck', 'unknown_body' ], example: 'sedan' },
                             number_of_doors: { type: :integer, example: 4 },
                             number_of_seats: { type: :integer, example: 5 },
                             engine_kilowatts: { type: :integer, nullable: true, example: 150 },
                             engine_number: { type: :string, nullable: true },
                             wheel_size_front: { type: :integer, nullable: true, example: 17 },
                             wheel_size_rear: { type: :integer, nullable: true, example: 17 },
                             odometer_reading: { type: :integer, nullable: true, example: 50000 },
                             odometer_date: { type: :string, format: :date, nullable: true },
                             redbook_code: { type: :string, nullable: true },
                             is_vehicle_present: { type: :boolean, example: true },
                             created_at: { type: :string, format: 'date-time', example: '2023-07-01T09:00:00.000Z' },
                             updated_at: { type: :string, format: 'date-time', example: '2023-07-01T09:00:00.000Z' },
                             main_photo_url: { type: :string, nullable: true, example: '/rails/active_storage/blobs/redirect/abc123/main_photo.jpg' },
                             odometer_reading_photo_url: { type: :string, nullable: true, example: '/rails/active_storage/blobs/redirect/def456/odometer_photo.jpg' },
                             photos_urls: { type: :array, items: { type: :string }, nullable: true, example: [ '/rails/active_storage/blobs/redirect/abc123/photo1.jpg', '/rails/active_storage/blobs/redirect/def456/photo2.jpg' ] },
                             customer_uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                             appraisal_uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                             brand_uuid: { type: :string, format: :uuid, nullable: true }
                           }
                         },
                         created_at: { type: :string, format: 'date-time', example: '2023-07-01T09:00:00.000Z' },
                         updated_at: { type: :string, format: 'date-time', example: '2023-07-01T09:00:00.000Z' }
                       }
                     }
                   },
                   required: [ 'appraisal' ]
                 }
               },
               required: [ 'status', 'data' ]

        let(:dealership_uuid) { dealership.uuid }
        let(:appraisal_uuid) { appraisal.uuid }
        let(:vehicle) do
          {
            make: "Toyota",
            model: "Camry",
            build_year: 2020,
            vin: "1HGBH41JXMN109186",
            rego: "ABC123",
            registration_state: "NSW",
            fuel_type: "petrol",
            transmission: "automatic",
            body_type: "sedan",
            number_of_doors: 4,
            number_of_seats: 5,
            odometer_reading: 50000,
            is_vehicle_present: true,
            exterior_color: "White",
            interior_color: "Black"
          }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(201)
          expect(json.dig("status", "message")).to eq("Vehicle created successfully")
          expect(json.dig("data", "appraisal")).to be_present
          expect(json.dig("data", "appraisal", "uuid")).to eq(appraisal.uuid)
          expect(json.dig("data", "appraisal", "status")).to eq("incomplete")
          expect(json.dig("data", "appraisal", "customer", "uuid")).to eq(customer.uuid)
          expect(json.dig("data", "appraisal", "sales_person", "uuid")).to eq(user.uuid)
          expect(json.dig("data", "appraisal", "created_by", "uuid")).to eq(user.uuid)
          expect(json.dig("data", "appraisal", "updated_by", "uuid")).to eq(user.uuid)
          expect(json.dig("data", "appraisal", "vehicle")).to be_present
          expect(json.dig("data", "appraisal", "vehicle", "uuid")).to be_present
          expect(json.dig("data", "appraisal", "vehicle", "make")).to eq("Toyota")
          expect(json.dig("data", "appraisal", "vehicle", "model")).to eq("Camry")
          expect(json.dig("data", "appraisal", "vehicle", "build_year")).to eq(2020)
          expect(json.dig("data", "appraisal", "vehicle", "vin")).to eq("1HGBH41JXMN109186")
          expect(json.dig("data", "appraisal", "vehicle", "rego")).to eq("ABC123")
          expect(json.dig("data", "appraisal", "vehicle", "registration_state")).to eq("NSW")
          expect(json.dig("data", "appraisal", "vehicle", "fuel_type")).to eq("petrol")
          expect(json.dig("data", "appraisal", "vehicle", "transmission")).to eq("automatic")
          expect(json.dig("data", "appraisal", "vehicle", "body_type")).to eq("sedan")
          expect(json.dig("data", "appraisal", "vehicle", "number_of_doors")).to eq(4)
          expect(json.dig("data", "appraisal", "vehicle", "number_of_seats")).to eq(5)
          expect(json.dig("data", "appraisal", "vehicle", "odometer_reading")).to eq(50000)
          expect(json.dig("data", "appraisal", "vehicle", "is_vehicle_present")).to eq(true)
          expect(json.dig("data", "appraisal", "vehicle", "exterior_color")).to eq("White")
          expect(json.dig("data", "appraisal", "vehicle", "interior_color")).to eq("Black")

          expect(json.dig("data", "appraisal", "created_at")).to be_present
          expect(json.dig("data", "appraisal", "updated_at")).to be_present
        end
      end

      response "422", "Missing required fields" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 422 },
                     message: { type: :string, example: "Validation failed: Make can't be blank" }
                   }
                 },
                 errors: {
                   type: :array,
                   items: {
                     type: :object,
                     properties: {
                       message: { type: :string, example: "Make can't be blank" }
                     }
                   }
                 }
               },
               required: [ 'status' ]

        let(:dealership_uuid) { dealership.uuid }
        let(:appraisal_uuid) { appraisal.uuid }
        let(:vehicle) { { make: nil, model: nil, build_year: nil } }

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(422)
          expect(json.dig("status", "message")).to include("Validation failed")
          expect(json["errors"]).to be_present
        end
      end

      response "422", "Appraisal already has a vehicle" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 422 },
                     message: { type: :string, example: "Appraisal already has a vehicle associated with it" }
                   }
                 }
               },
               required: [ 'status' ]

        let(:dealership_uuid) { dealership.uuid }
        let(:appraisal_uuid) { appraisal.uuid }
        let(:vehicle) { { make: "Toyota", model: "Camry", build_year: 2020 } }
        let!(:existing_vehicle) do
          create(:customer_vehicle,
            appraisal: appraisal,
            customer: appraisal.customer,
            dealership: appraisal.dealership
          )
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(422)
          expect(json.dig("status", "message")).to eq("Appraisal already has a vehicle associated with it")
        end
      end

      response "422", "Cannot create vehicle for archived appraisal" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 422 },
                     message: { type: :string, example: "Cannot create vehicle for archived appraisal" }
                   }
                 }
               },
               required: [ 'status' ]

        let(:dealership_uuid) { dealership.uuid }
        let(:appraisal_uuid) { archived_appraisal.uuid }
        let(:archived_appraisal) { create(:appraisal, dealership: dealership, customer: customer, sales_person: user, status: :archived) }
        let(:vehicle) { { make: "Toyota", model: "Camry", build_year: 2020 } }

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(422)
          expect(json.dig("status", "message")).to eq("Cannot create vehicle for archived appraisal")
        end
      end

      response "404", "Appraisal not found" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 404 },
                     message: { type: :string, example: "Appraisal not found or does not belong to this dealership" }
                   }
                 }
               },
               required: [ 'status' ]

        let(:dealership_uuid) { dealership.uuid }
        let(:appraisal_uuid) { "non-existent-uuid" }
        let(:vehicle) { { make: "Toyota", model: "Camry", build_year: 2020 } }

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(404)
          expect(json.dig("status", "message")).to eq("Appraisal not found or does not belong to this dealership")
        end
      end

      response "404", "Dealership not found" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 404 },
                     message: { type: :string, example: "Dealership not found or you don't have access to it" }
                   }
                 }
               },
               required: [ 'status' ]

        let(:dealership_uuid) { "non-existent-uuid" }
        let(:appraisal_uuid) { appraisal.uuid }
        let(:vehicle) { { make: "Toyota", model: "Camry", build_year: 2020 } }

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(404)
          expect(json.dig("status", "message")).to eq("Dealership not found or you don't have access to it")
        end
      end

      response "401", "Missing authorization token" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 401 },
                     message: { type: :string, example: "Missing authorization token" }
                   }
                 }
               },
               required: [ 'status' ]

        let(:Authorization) { nil }
        let(:dealership_uuid) { dealership.uuid }
        let(:appraisal_uuid) { appraisal.uuid }
        let(:vehicle) { { make: "Toyota", model: "Camry", build_year: 2020 } }

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(401)
          expect(json.dig("status", "message")).to eq("Missing authorization token")
        end
      end

      response "401", "Invalid device" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 401 },
                     message: { type: :string, example: "Invalid device" }
                   }
                 }
               },
               required: [ 'status' ]

        let(:'Device-ID') { nil }
        let(:dealership_uuid) { dealership.uuid }
        let(:appraisal_uuid) { appraisal.uuid }
        let(:vehicle) { { make: "Toyota", model: "Camry", build_year: 2020 } }

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(401)
          expect(json.dig("status", "message")).to eq("Invalid device")
        end
      end
    end
  end
end
