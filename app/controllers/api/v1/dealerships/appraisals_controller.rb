class Api::V1::Dealerships::AppraisalsController < Api::V1::BaseController
  include DateTimeFormatHelper
  before_action :set_appraisal, only: [ :show ]

  def index
    appraisals = filtered_appraisals
    @pagy, @appraisals = pagy(appraisals, limit: pagination_validated_per_page, page: params[:page] || 1)
    set_pagination_headers(@pagy)
    render :index
  end
  def show
    render :show
  end


  def create
    validate_create_params!

    @appraisal = dealership.appraisals.new(
      customer:,
      sales_person: current_user,
      created_by: current_user,
      updated_by: current_user,
      status: :incomplete
    )

    @appraisal.save!
    @status_code = 201
    @status_message = "Appraisal created successfully"
    render :show, status: :created
  end

  def create_vehicle
    validate_create_vehicle_params!

    @customer_vehicle = appraisal.build_customer_vehicle(vehicle_params.except(:brand_uuid))
    @customer_vehicle.customer = appraisal.customer
    @customer_vehicle.dealership = appraisal.dealership
    @customer_vehicle.brand = find_brand(vehicle_params[:brand_uuid]) if vehicle_params[:brand_uuid].present?

    @customer_vehicle.save!
    @appraisal = appraisal.reload
    @status_code = 201
    @status_message = "Vehicle created successfully"
    render :show, status: :created
  end

  def update_vehicle
    validate_update_vehicle_params!

    @customer_vehicle = appraisal.customer_vehicle
    @customer_vehicle.brand = find_brand(vehicle_params[:brand_uuid]) if vehicle_params[:brand_uuid].present?
    @customer_vehicle.update!(vehicle_params.except(:brand_uuid))
    @appraisal = appraisal.reload
    @status_message = "Vehicle updated successfully"
    render :show
  end

  private

  def set_appraisal
    @appraisal = dealership.appraisals
                          .includes(:customer, :sales_person, :created_by, :updated_by,
                                   { customer_vehicle: :brand })
                          .where.not(status: :deleted)
                          .find_by!(uuid: params[:appraisal_uuid])
  rescue ActiveRecord::RecordNotFound
    raise Errors::RecordNotFound, "Appraisal not found"
  end

  def filtered_appraisals
    appraisals = dealership.appraisals.includes(:customer, :sales_person, :created_by, :updated_by, customer_vehicle: :brand)

    appraisals = appraisals.filter_by_status(index_params[:status]) if index_params[:status].present?
    appraisals = appraisals.by_salesperson(current_user.uuid) if index_params[:only_mine].present?
    appraisals = appraisals.by_customer(index_params[:customer_uuid]) if index_params[:customer_uuid].present?
    appraisals = appraisals.by_salesperson(index_params[:salesperson_uuid]) if index_params[:salesperson_uuid].present?
    appraisals = appraisals.by_brand(index_params[:brand_uuid]) if index_params[:brand_uuid].present?
    appraisals = appraisals.by_registration_number(index_params[:registration_number]) if index_params[:registration_number].present?
    appraisals = appraisals.created_between_dates(index_params[:start_date], index_params[:end_date]) if date_filters_present?
    appraisals = appraisals.search_by_term(index_params[:query]) if index_params[:query].present?

    appraisals.order(updated_at: :desc)
  end

  def date_filters_present?
    index_params[:start_date].present? || index_params[:end_date].present?
  end

  def index_params
    params.permit(:status, :only_mine, :query, :brand_uuid, :registration_number,
                  :salesperson_uuid, :start_date, :end_date, :customer_uuid)
  end

  def validate_create_params!
    raise Errors::InvalidInput, "customer_uuid is required" if permitted_params[:customer_uuid].blank?
  end

  def validate_create_vehicle_params!
    if appraisal.archived? || appraisal.deleted?
      raise Errors::InvalidInput, "Cannot create vehicle for #{appraisal.status} appraisal"
    end

    if appraisal.customer_vehicle.present?
      raise Errors::InvalidInput, "Appraisal already has a vehicle associated with it"
    end
  end

  def validate_update_vehicle_params!
    if appraisal.archived? || appraisal.deleted?
      raise Errors::InvalidInput, "Cannot update vehicle for #{appraisal.status} appraisal"
    end

    if appraisal.customer_vehicle.blank?
      raise Errors::InvalidInput, "No vehicle found for this appraisal"
    end
  end

  def vehicle_params
    params.expect(
      vehicle: [ :brand_uuid, :make, :model, :vin, :rego, :registration_expiry, :registration_state,
               :build_year, :build_month, :compliance_year, :compliance_month,
               :exterior_color, :interior_color, :seat_type, :fuel_type, :driving_wheels,
               :spare_wheel_type, :transmission, :body_type, :number_of_doors, :number_of_seats,
               :engine_kilowatts, :engine_number, :wheel_size_front, :wheel_size_rear, :odometer_reading, :odometer_date,
               :is_vehicle_present, :redbook_code, :main_photo, :odometer_reading_photo, photos: [] ]
    )
  end

  def permitted_params
    params.permit(:customer_uuid)
  end
end
